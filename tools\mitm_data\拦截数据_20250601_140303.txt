
============================================================
时间戳: 2025-06-01T14:03:05.997502
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 39 字节
原始数据(HEX): 323230204c4150544f502d503231524534505520507974686f6e20534d545020312e342e360d0a
原始数据(UTF-8): 220 LAPTOP-P21RE4PU Python SMTP 1.4.6


============================================================

============================================================
时间戳: 2025-06-01T14:03:05.999011
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 22 字节
原始数据(HEX): 65686c6f205b3139322e3136382e3133372e315d0d0a
原始数据(UTF-8): ehlo [192.168.137.1]


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.006020
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 21 字节
原始数据(HEX): 3235302d4c4150544f502d50323152453450550d0a
原始数据(UTF-8): 250-LAPTOP-P21RE4PU


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.007021
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 65 字节
原始数据(HEX): 3235302d53495a452031303438353736300d0a3235302d534d5450555446380d0a3235302d41555448204c4f47494e20504c41494e0d0a3235302048454c500d0a
原始数据(UTF-8): 250-SIZE 10485760

250-SMTPUTF8

250-AUTH LOGIN PLAIN

250 HELP


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.009549
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 4155544820504c41494e204148526c63335231633256794148526c6333527759584e7a0d0a
原始数据(UTF-8): AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz


🔓 认证信息已破解:
    🔓 SMTP PLAIN认证: 用户名='testuser', 密码='testpass'
============================================================

============================================================
时间戳: 2025-06-01T14:03:06.017558
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 41757468656e7469636174696f6e207375636365737366756c0d0a
原始数据(UTF-8): Authentication successful


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.019208
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 25 字节
原始数据(HEX): 41555448204c4f47494e206447567a6448567a5a58493d0d0a
原始数据(UTF-8): AUTH LOGIN dGVzdHVzZXI=


🔓 认证信息已破解:
    🔓 SMTP LOGIN用户名: testuser
============================================================

============================================================
时间戳: 2025-06-01T14:03:06.020077
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 35303320416c72656164792061757468656e746963617465640d0a
原始数据(UTF-8): 503 Already authenticated


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.022079
方向: 客户端->服务器
协议: 明文
类型: 未知
大小: 6 字节
原始数据(HEX): 717569740d0a
原始数据(UTF-8): quit


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.023081
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 9 字节
原始数据(HEX): 323231204279650d0a
原始数据(UTF-8): 221 Bye


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.030608
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 39 字节
原始数据(HEX): 323230204c4150544f502d503231524534505520507974686f6e20534d545020312e342e360d0a
原始数据(UTF-8): 220 LAPTOP-P21RE4PU Python SMTP 1.4.6


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.033608
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 22 字节
原始数据(HEX): 65686c6f205b3139322e3136382e3133372e315d0d0a
原始数据(UTF-8): ehlo [192.168.137.1]


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.035609
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 21 字节
原始数据(HEX): 3235302d4c4150544f502d50323152453450550d0a
原始数据(UTF-8): 250-LAPTOP-P21RE4PU


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.036606
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 65 字节
原始数据(HEX): 3235302d53495a452031303438353736300d0a3235302d534d5450555446380d0a3235302d41555448204c4f47494e20504c41494e0d0a3235302048454c500d0a
原始数据(UTF-8): 250-SIZE 10485760

250-SMTPUTF8

250-AUTH LOGIN PLAIN

250 HELP


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.037668
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 4155544820504c41494e204148526c63335231633256794148526c6333527759584e7a0d0a
原始数据(UTF-8): AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz


🔓 认证信息已破解:
    🔓 SMTP PLAIN认证: 用户名='testuser', 密码='testpass'
============================================================

============================================================
时间戳: 2025-06-01T14:03:06.042184
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 41757468656e7469636174696f6e207375636365737366756c0d0a
原始数据(UTF-8): Authentication successful


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.043187
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 25 字节
原始数据(HEX): 41555448204c4f47494e206447567a6448567a5a58493d0d0a
原始数据(UTF-8): AUTH LOGIN dGVzdHVzZXI=


🔓 认证信息已破解:
    🔓 SMTP LOGIN用户名: testuser
============================================================

============================================================
时间戳: 2025-06-01T14:03:06.045186
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 35303320416c72656164792061757468656e746963617465640d0a
原始数据(UTF-8): 503 Already authenticated


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.047188
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 43 字节
原始数据(HEX): 6d61696c2046524f4d3a3c7465737475736572406578616d706c652e636f6d3e2073697a653d3636370d0a
原始数据(UTF-8): mail FROM:<<EMAIL>> size=667


⚠️ 其他敏感信息:
    ⚠️ 发件人地址: <EMAIL>...
    ⚠️ 发件人信息: <<EMAIL>> size=667...
============================================================

============================================================
时间戳: 2025-06-01T14:03:06.049778
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 8 字节
原始数据(HEX): 323530204f4b0d0a
原始数据(UTF-8): 250 OK


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.050788
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 33 字节
原始数据(HEX): 7263707420544f3a3c726563697069656e74406578616d706c652e636f6d3e0d0a
原始数据(UTF-8): rcpt TO:<<EMAIL>>


⚠️ 其他敏感信息:
    ⚠️ 收件人地址: <EMAIL>...
    ⚠️ 收件人信息: <<EMAIL>>...
============================================================

============================================================
时间戳: 2025-06-01T14:03:06.052785
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 8 字节
原始数据(HEX): 323530204f4b0d0a
原始数据(UTF-8): 250 OK


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.053786
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 6 字节
原始数据(HEX): 646174610d0a
原始数据(UTF-8): data


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.054788
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 33353420456e6420646174612077697468203c43523e3c4c463e2e3c43523e3c4c463e0d0a
原始数据(UTF-8): 354 End data with <CR><LF>.<CR><LF>


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.055786
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 670 字节
原始数据(HEX): 436f6e74656e742d547970653a206d756c7469706172742f6d697865643b20626f756e646172793d223d3d3d3d3d3d3d3d3d3d3d3d3d3d3d363135313830333937303632363631303430393d3d220d0a4d494d452d56657273696f6e3a20312e300d0a4d6573736167652d49443a203c746573745f32303235303630315f313430333035406578616d706c652e636f6d3e0d0a5375626a6563743a203d3f7574662d383f423f3572574c364b2b563570533735596537494673794d4449314c5441324c544178494445304f6a417a4f6a417758513d3d3f3d0d0a46726f6d3a203d3f7574662d383f623f3572574c364b2b5635592b5236594342364943463f3d203c7465737475736572406578616d706c652e636f6d3e0d0a546f3a203d3f7574662d383f623f3572574c364b2b56356f366c35705332364943463f3d203c726563697069656e74406578616d706c652e636f6d3e0d0a446174653a2053756e2c203031204a756e20323032352031343a30333a3035202b303830300d0a0d0a2d2d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d363135313830333937303632363631303430393d3d0d0a436f6e74656e742d547970653a20746578742f706c61696e3b20636861727365743d227574662d38220d0a4d494d452d56657273696f6e3a20312e300d0a436f6e74656e742d5472616e736665722d456e636f64696e673a206261736536340d0a0d0a35705337355965373572574c364b2b564367726c6a3548706749486d6c3762706c375136494449774d6a55744d4459744d4445674d5451364d444d364d44414b35597946355a4372357057500d0a356f5366354c2b68356f47764f69446d6d4b383d0d0a0d0a2d2d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d363135313830333937303632363631303430393d3d2d2d0d0a2e0d0a
原始数据(UTF-8): Content-Type: multipart/mixed; boundary="===============6151803970626610409=="

MIME-Version: 1.0

Message-ID: <<EMAIL>>

Subject: =?utf-8?B?5rWL6K+V5pS75Ye7IFsyMDI1LTA2LTAxIDE0OjAzOjAwXQ==?=

From: =?utf-8?b?5rWL6K+V5Y+R6YCB6ICF?= <<EMAIL>>

To: =?utf-8?b?5rWL6K+V5o6l5pS26ICF?= <<EMAIL>>

Date: Sun, 01 Jun 2025 14:03:05 +0800



--===============6151803970626610409==

Content-Type: text/plain; charset="utf-8"

MIME-Version: 1.0

Content-Transfer-Encoding: base64



5pS75Ye75rWL6K+VCgrlj5HpgIHml7bpl7Q6IDIwMjUtMDYtMDEgMTQ6MDM6MDAK5YyF5ZCr5pWP

5oSf5L+h5oGvOiDmmK8=



--===============6151803970626610409==--

.


📧 邮件内容已解码:
    📧 邮件头部编码: 测试攻击 [2025-06-01 14:03:00]...
    📧 邮件头部编码: 测试发送者...
    📧 邮件头部编码: 测试接收者...
    📧 邮件正文内容: 攻击测试

发送时间: 2025-06-01 14:03:00
包含敏...
    📧 邮件正文内容: 感信息: 是...
============================================================

============================================================
时间戳: 2025-06-01T14:03:06.067308
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 35 字节
原始数据(HEX): 323530204d65737361676520616363657074656420666f722064656c69766572790d0a
原始数据(UTF-8): 250 Message accepted for delivery


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.074826
方向: 客户端->服务器
协议: 明文
类型: 未知
大小: 6 字节
原始数据(HEX): 717569740d0a
原始数据(UTF-8): quit


============================================================

============================================================
时间戳: 2025-06-01T14:03:06.075827
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 9 字节
原始数据(HEX): 323231204279650d0a
原始数据(UTF-8): 221 Bye


============================================================

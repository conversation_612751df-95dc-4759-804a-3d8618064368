<?xml version="1.0" encoding="UTF-8"?>
<svg width="841mm" height="594mm" viewBox="0 0 3189 2245" xmlns="http://www.w3.org/2000/svg">
  <!-- A1 Landscape Academic Poster for CS3611 Email System -->
  <!-- Scaled up for better rendering: 1mm = 3.78 pixels -->

  <!-- Background -->
  <rect width="3189" height="2245" fill="#f8f9fa"/>

  <!-- Header Section -->
  <rect x="76" y="76" width="3037" height="378" fill="#ffffff" stroke="#2c3e50" stroke-width="6" rx="15"/>

  <!-- Main Title -->
  <text x="1595" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="96" font-weight="bold" fill="#2c3e50">
    CS3611 Email System Implementation
  </text>

  <!-- Subtitle -->
  <text x="1595" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="64" fill="#34495e">
    Complete SMTP/POP3 Client-Server System with SSL/TLS Security
  </text>

  <!-- Author Information -->
  <text x="1595" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" fill="#7f8c8d">
    Computer Networks Course Project | CS3611 | 2024
  </text>

  <!-- Main Content Area -->

  <!-- Abstract Section -->
  <rect x="114" y="530" width="910" height="680" fill="#ffffff" stroke="#3498db" stroke-width="4" rx="10"/>
  <text x="569" y="620" text-anchor="middle" font-family="Arial, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
    Project Overview
  </text>
  <text x="150" y="700" font-family="Arial, sans-serif" font-size="42" fill="#2c3e50">
    <tspan x="150" dy="0">This project implements a complete email</tspan>
    <tspan x="150" dy="55">communication system with both client</tspan>
    <tspan x="150" dy="55">and server components.</tspan>
    <tspan x="150" dy="80">✓ SMTP/POP3 protocol implementation</tspan>
    <tspan x="150" dy="55">✓ SSL/TLS encrypted communication</tspan>
    <tspan x="150" dy="55">✓ Multi-user concurrent support</tspan>
    <tspan x="150" dy="55">✓ Complete .eml file handling</tspan>
    <tspan x="150" dy="55">✓ SQLite database storage</tspan>
    <tspan x="150" dy="55">✓ Web interface and CLI tools</tspan>
    <tspan x="150" dy="55">✓ Spam filtering capabilities</tspan>
  </text>

  <!-- System Architecture Section -->
  <rect x="1139" y="530" width="985" height="680" fill="#ffffff" stroke="#3498db" stroke-width="4" rx="10"/>
  <text x="1632" y="620" text-anchor="middle" font-family="Arial, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
    System Architecture
  </text>

  <!-- Architecture Diagram -->
  <g transform="translate(1200, 680)">
    <!-- Client Layer -->
    <rect x="40" y="40" width="240" height="80" fill="#e74c3c" rx="10"/>
    <text x="160" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">SMTP Client</text>

    <rect x="320" y="40" width="240" height="80" fill="#e74c3c" rx="10"/>
    <text x="440" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">POP3 Client</text>

    <rect x="600" y="40" width="200" height="80" fill="#e74c3c" rx="10"/>
    <text x="700" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">CLI/Web</text>

    <!-- SSL/TLS Layer -->
    <rect x="40" y="160" width="760" height="60" fill="#f39c12" rx="10"/>
    <text x="420" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="white">SSL/TLS Encryption Layer</text>

    <!-- Server Layer -->
    <rect x="40" y="260" width="240" height="80" fill="#27ae60" rx="10"/>
    <text x="160" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">SMTP Server</text>

    <rect x="320" y="260" width="240" height="80" fill="#27ae60" rx="10"/>
    <text x="440" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">POP3 Server</text>

    <rect x="600" y="260" width="200" height="80" fill="#27ae60" rx="10"/>
    <text x="700" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">Auth System</text>

    <!-- Database Layer -->
    <rect x="200" y="380" width="400" height="80" fill="#9b59b6" rx="10"/>
    <text x="400" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">SQLite + .eml Storage</text>

    <!-- Connection Lines -->
    <line x1="160" y1="120" x2="160" y2="260" stroke="#34495e" stroke-width="4"/>
    <line x1="440" y1="120" x2="440" y2="260" stroke="#34495e" stroke-width="4"/>
    <line x1="700" y1="120" x2="700" y2="260" stroke="#34495e" stroke-width="4"/>
    <line x1="160" y1="340" x2="400" y2="380" stroke="#34495e" stroke-width="4"/>
    <line x1="440" y1="340" x2="400" y2="380" stroke="#34495e" stroke-width="4"/>
    <line x1="700" y1="340" x2="400" y2="380" stroke="#34495e" stroke-width="4"/>
  </g>

  <!-- Key Features Section -->
  <rect x="2239" y="530" width="910" height="680" fill="#ffffff" stroke="#3498db" stroke-width="4" rx="10"/>
  <text x="2694" y="620" text-anchor="middle" font-family="Arial, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
    Core Features
  </text>
  <text x="2280" y="700" font-family="Arial, sans-serif" font-size="42" fill="#2c3e50">
    <tspan x="2280" dy="0">🔒 Security Implementation:</tspan>
    <tspan x="2280" dy="55">• SSL/TLS encryption support</tspan>
    <tspan x="2280" dy="55">• LOGIN &amp; AUTH PLAIN commands</tspan>
    <tspan x="2280" dy="55">• Secure password handling</tspan>
    <tspan x="2280" dy="80">📧 Email Processing:</tspan>
    <tspan x="2280" dy="55">• SMTP sending with attachments</tspan>
    <tspan x="2280" dy="55">• POP3 email retrieval</tspan>
    <tspan x="2280" dy="55">• .eml format storage</tspan>
    <tspan x="2280" dy="80">⚡ Server Capabilities:</tspan>
    <tspan x="2280" dy="55">• 10+ concurrent connections</tspan>
    <tspan x="2280" dy="55">• SQLite metadata storage</tspan>
    <tspan x="2280" dy="55">• Spam filtering system</tspan>
  </text>

  <!-- Implementation Details Section -->
  <rect x="114" y="1290" width="910" height="530" fill="#ffffff" stroke="#3498db" stroke-width="4" rx="10"/>
  <text x="569" y="1380" text-anchor="middle" font-family="Arial, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
    Implementation Details
  </text>
  <text x="150" y="1460" font-family="Arial, sans-serif" font-size="42" fill="#2c3e50">
    <tspan x="150" dy="0">📧 Protocol Compliance:</tspan>
    <tspan x="150" dy="55">• Complete SMTP/POP3 RFC standards</tspan>
    <tspan x="150" dy="55">• MIME multipart message handling</tspan>
    <tspan x="150" dy="55">• Attachment encoding/decoding</tspan>
    <tspan x="150" dy="80">🏗️ Architecture Design:</tspan>
    <tspan x="150" dy="55">• Modular client-server structure</tspan>
    <tspan x="150" dy="55">• Unified email format processing</tspan>
    <tspan x="150" dy="55">• Comprehensive error handling</tspan>
  </text>

  <!-- Performance & Testing Section -->
  <rect x="1139" y="1290" width="985" height="530" fill="#ffffff" stroke="#3498db" stroke-width="4" rx="10"/>
  <text x="1632" y="1380" text-anchor="middle" font-family="Arial, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
    Performance &amp; Testing
  </text>

  <!-- Performance Chart -->
  <g transform="translate(1200, 1420)">
    <!-- Chart Background -->
    <rect x="40" y="40" width="800" height="300" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="4" rx="10"/>

    <!-- Chart Title -->
    <text x="440" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="40" font-weight="bold" fill="#2c3e50">
      Concurrent Connection Support
    </text>

    <!-- Y-axis labels -->
    <text x="20" y="140" font-family="Arial, sans-serif" font-size="32" fill="#7f8c8d">15</text>
    <text x="20" y="200" font-family="Arial, sans-serif" font-size="32" fill="#7f8c8d">10</text>
    <text x="20" y="260" font-family="Arial, sans-serif" font-size="32" fill="#7f8c8d">5</text>
    <text x="20" y="320" font-family="Arial, sans-serif" font-size="32" fill="#7f8c8d">0</text>

    <!-- SMTP Bar -->
    <rect x="200" y="120" width="200" height="180" fill="#e74c3c" rx="10"/>
    <text x="300" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" fill="#2c3e50">SMTP</text>
    <text x="300" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="40" font-weight="bold" fill="white">10+</text>

    <!-- POP3 Bar -->
    <rect x="480" y="120" width="200" height="180" fill="#27ae60" rx="10"/>
    <text x="580" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" fill="#2c3e50">POP3</text>
    <text x="580" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="40" font-weight="bold" fill="white">10+</text>

    <!-- Testing Results -->
    <text x="440" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" fill="#2c3e50">
      ✓ All functional tests passed
    </text>
  </g>

  <!-- Extended Features Section -->
  <rect x="2239" y="1290" width="910" height="530" fill="#ffffff" stroke="#3498db" stroke-width="4" rx="10"/>
  <text x="2694" y="1380" text-anchor="middle" font-family="Arial, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
    Extended Features
  </text>
  <text x="2280" y="1460" font-family="Arial, sans-serif" font-size="42" fill="#2c3e50">
    <tspan x="2280" dy="0">🛡️ Spam Filtering:</tspan>
    <tspan x="2280" dy="55">• Keyword-based detection</tspan>
    <tspan x="2280" dy="55">• Bayesian classification</tspan>
    <tspan x="2280" dy="55">• Automatic spam marking</tspan>
    <tspan x="2280" dy="80">📧 Email Management:</tspan>
    <tspan x="2280" dy="55">• Email recall functionality</tspan>
    <tspan x="2280" dy="55">• Unique message ID tracking</tspan>
    <tspan x="2280" dy="80">🌐 User Interfaces:</tspan>
    <tspan x="2280" dy="55">• Interactive CLI system</tspan>
    <tspan x="2280" dy="55">• Web-based management</tspan>
  </text>

  <!-- Footer Section -->
  <rect x="76" y="1900" width="3037" height="265" fill="#ffffff" stroke="#2c3e50" stroke-width="6" rx="15"/>

  <!-- Conclusion -->
  <text x="150" y="1980" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#2c3e50">
    Conclusion &amp; Educational Value
  </text>
  <text x="150" y="2040" font-family="Arial, sans-serif" font-size="38" fill="#2c3e50">
    Successfully implemented a complete email system demonstrating mastery of network protocols,
  </text>
  <text x="150" y="2085" font-family="Arial, sans-serif" font-size="38" fill="#2c3e50">
    security implementation, and software engineering best practices for educational purposes.
  </text>

  <!-- Contact Information -->
  <text x="1800" y="1980" font-family="Arial, sans-serif" font-size="42" font-weight="bold" fill="#2c3e50">
    Project Resources
  </text>
  <text x="1800" y="2030" font-family="Arial, sans-serif" font-size="36" fill="#3498db">
    📁 Complete source code available
  </text>
  <text x="1800" y="2070" font-family="Arial, sans-serif" font-size="36" fill="#3498db">
    📖 Comprehensive documentation
  </text>
  <text x="1800" y="2110" font-family="Arial, sans-serif" font-size="36" fill="#3498db">
    🧪 Full test suite included
  </text>

  <!-- QR Code Placeholder -->
  <rect x="2850" y="1950" width="150" height="150" fill="#2c3e50" rx="10"/>
  <text x="2925" y="2010" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="white">QR CODE</text>
  <text x="2925" y="2040" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="white">Project</text>
  <text x="2925" y="2065" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="white">Repository</text>

  <!-- Decorative Elements -->
  <circle cx="300" cy="300" r="12" fill="#3498db"/>
  <circle cx="2889" cy="300" r="12" fill="#3498db"/>
  <circle cx="300" cy="2000" r="12" fill="#3498db"/>
  <circle cx="2889" cy="2000" r="12" fill="#3498db"/>

</svg>

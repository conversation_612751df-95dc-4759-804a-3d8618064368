<?xml version="1.0" encoding="UTF-8"?>
<svg width="841mm" height="594mm" viewBox="0 0 841 594" xmlns="http://www.w3.org/2000/svg">
  <!-- A1 Landscape Academic Poster for CS3611 Email System -->
  
  <!-- Background -->
  <rect width="841" height="594" fill="#f8f9fa"/>
  
  <!-- Header Section (20%) -->
  <rect x="20" y="20" width="801" height="100" fill="#ffffff" stroke="#2c3e50" stroke-width="2"/>
  
  <!-- Main Title -->
  <text x="421" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
    CS3611 Email System Implementation
  </text>
  
  <!-- Subtitle -->
  <text x="421" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="#34495e">
    Secure Email Communication with SMTP/POP3 and SSL/TLS Encryption
  </text>
  
  <!-- Author Information -->
  <text x="421" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#7f8c8d">
    Computer Networks Course Project | CS3611 | 2024
  </text>
  
  <!-- Main Content Area (70%) -->
  
  <!-- Abstract Section -->
  <rect x="30" y="140" width="240" height="180" fill="#ffffff" stroke="#3498db" stroke-width="1"/>
  <text x="150" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">
    Abstract
  </text>
  <text x="40" y="180" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    <tspan x="40" dy="0">This project implements a complete email</tspan>
    <tspan x="40" dy="15">system featuring SMTP and POP3 servers</tspan>
    <tspan x="40" dy="15">with corresponding clients. The system</tspan>
    <tspan x="40" dy="15">supports SSL/TLS encryption, user</tspan>
    <tspan x="40" dy="15">authentication, high concurrency</tspan>
    <tspan x="40" dy="15">processing, and modern web interface.</tspan>
    <tspan x="40" dy="25">Key achievements:</tspan>
    <tspan x="40" dy="15">• 200+ concurrent SMTP connections</tspan>
    <tspan x="40" dy="15">• 100+ concurrent POP3 connections</tspan>
    <tspan x="40" dy="15">• SSL/TLS security implementation</tspan>
    <tspan x="40" dy="15">• Complete RFC compliance</tspan>
  </text>
  
  <!-- System Architecture Section -->
  <rect x="290" y="140" width="260" height="180" fill="#ffffff" stroke="#3498db" stroke-width="1"/>
  <text x="420" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">
    System Architecture
  </text>
  
  <!-- Architecture Diagram -->
  <g transform="translate(300, 170)">
    <!-- Client Layer -->
    <rect x="10" y="10" width="80" height="25" fill="#e74c3c" rx="3"/>
    <text x="50" y="27" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">SMTP Client</text>
    
    <rect x="100" y="10" width="80" height="25" fill="#e74c3c" rx="3"/>
    <text x="140" y="27" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">POP3 Client</text>
    
    <rect x="190" y="10" width="50" height="25" fill="#e74c3c" rx="3"/>
    <text x="215" y="27" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Web UI</text>
    
    <!-- SSL/TLS Layer -->
    <rect x="10" y="50" width="230" height="15" fill="#f39c12" rx="3"/>
    <text x="125" y="62" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">SSL/TLS Encryption Layer</text>
    
    <!-- Server Layer -->
    <rect x="10" y="80" width="80" height="25" fill="#27ae60" rx="3"/>
    <text x="50" y="97" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">SMTP Server</text>
    
    <rect x="100" y="80" width="80" height="25" fill="#27ae60" rx="3"/>
    <text x="140" y="97" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">POP3 Server</text>
    
    <rect x="190" y="80" width="50" height="25" fill="#27ae60" rx="3"/>
    <text x="215" y="97" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Auth</text>
    
    <!-- Database Layer -->
    <rect x="60" y="120" width="120" height="25" fill="#9b59b6" rx="3"/>
    <text x="120" y="137" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">SQLite Database + File Storage</text>
    
    <!-- Connection Lines -->
    <line x1="50" y1="35" x2="50" y2="80" stroke="#34495e" stroke-width="1"/>
    <line x1="140" y1="35" x2="140" y2="80" stroke="#34495e" stroke-width="1"/>
    <line x1="215" y1="35" x2="215" y2="80" stroke="#34495e" stroke-width="1"/>
    <line x1="50" y1="105" x2="120" y2="120" stroke="#34495e" stroke-width="1"/>
    <line x1="140" y1="105" x2="120" y2="120" stroke="#34495e" stroke-width="1"/>
    <line x1="215" y1="105" x2="120" y2="120" stroke="#34495e" stroke-width="1"/>
  </g>
  
  <!-- Key Features Section -->
  <rect x="570" y="140" width="240" height="180" fill="#ffffff" stroke="#3498db" stroke-width="1"/>
  <text x="690" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">
    Key Features
  </text>
  <text x="580" y="180" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    <tspan x="580" dy="0">🔒 Security Features:</tspan>
    <tspan x="580" dy="15">• SSL/TLS encryption (TLS 1.2+)</tspan>
    <tspan x="580" dy="15">• User authentication (LOGIN/PLAIN/APOP)</tspan>
    <tspan x="580" dy="15">• Password hashing with bcrypt</tspan>
    <tspan x="580" dy="15">• Self-signed certificate generation</tspan>
    <tspan x="580" dy="25">⚡ Performance Features:</tspan>
    <tspan x="580" dy="15">• High concurrency (200+ SMTP, 100+ POP3)</tspan>
    <tspan x="580" dy="15">• Asynchronous processing</tspan>
    <tspan x="580" dy="15">• Connection pooling</tspan>
    <tspan x="580" dy="15">• SQLite WAL mode optimization</tspan>
    <tspan x="580" dy="25">🌐 Interface Features:</tspan>
    <tspan x="580" dy="15">• Modern web interface (Flask)</tspan>
    <tspan x="580" dy="15">• Interactive CLI menu system</tspan>
    <tspan x="580" dy="15">• MIME handling and attachments</tspan>
  </text>
  
  <!-- Implementation Highlights Section -->
  <rect x="30" y="340" width="240" height="160" fill="#ffffff" stroke="#3498db" stroke-width="1"/>
  <text x="150" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">
    Implementation Highlights
  </text>
  <text x="40" y="380" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    <tspan x="40" dy="0">📧 Protocol Compliance:</tspan>
    <tspan x="40" dy="15">• Full SMTP/POP3 RFC implementation</tspan>
    <tspan x="40" dy="15">• RFC 2047 email encoding support</tspan>
    <tspan x="40" dy="15">• MIME multipart handling</tspan>
    <tspan x="40" dy="25">🏗️ Architecture Design:</tspan>
    <tspan x="40" dy="15">• Modular component structure</tspan>
    <tspan x="40" dy="15">• Unified email format handling</tspan>
    <tspan x="40" dy="15">• Comprehensive error handling</tspan>
    <tspan x="40" dy="25">🧪 Testing & Quality:</tspan>
    <tspan x="40" dy="15">• 100% test pass rate</tspan>
    <tspan x="40" dy="15">• Unit, integration & performance tests</tspan>
    <tspan x="40" dy="15">• Stress testing up to 200+ connections</tspan>
  </text>
  
  <!-- Performance Results Section -->
  <rect x="290" y="340" width="260" height="160" fill="#ffffff" stroke="#3498db" stroke-width="1"/>
  <text x="420" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">
    Performance Results
  </text>
  
  <!-- Performance Chart -->
  <g transform="translate(300, 370)">
    <!-- Chart Background -->
    <rect x="10" y="10" width="240" height="100" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
    
    <!-- Chart Title -->
    <text x="130" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2c3e50">
      Concurrent Connection Performance
    </text>
    
    <!-- Y-axis labels -->
    <text x="5" y="45" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">200</text>
    <text x="5" y="65" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">100</text>
    <text x="5" y="85" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">50</text>
    <text x="5" y="105" font-family="Arial, sans-serif" font-size="9" fill="#7f8c8d">0</text>
    
    <!-- SMTP Bar -->
    <rect x="50" y="40" width="60" height="60" fill="#e74c3c"/>
    <text x="80" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2c3e50">SMTP</text>
    <text x="80" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">200+</text>
    
    <!-- POP3 Bar -->
    <rect x="130" y="60" width="60" height="40" fill="#27ae60"/>
    <text x="160" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2c3e50">POP3</text>
    <text x="160" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">100+</text>
    
    <!-- Response Time -->
    <text x="130" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2c3e50">
      Response Time: &lt;3s (SMTP), &lt;1s (POP3)
    </text>
  </g>
  
  <!-- Security Analysis Section -->
  <rect x="570" y="340" width="240" height="160" fill="#ffffff" stroke="#3498db" stroke-width="1"/>
  <text x="690" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">
    Security Analysis
  </text>
  <text x="580" y="380" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">
    <tspan x="580" dy="0">🛡️ Encryption Implementation:</tspan>
    <tspan x="580" dy="15">• TLS 1.2+ with strong cipher suites</tspan>
    <tspan x="580" dy="15">• ECDHE+AESGCM, ECDHE+CHACHA20</tspan>
    <tspan x="580" dy="15">• Certificate-based authentication</tspan>
    <tspan x="580" dy="25">🔐 Authentication Security:</tspan>
    <tspan x="580" dy="15">• bcrypt password hashing</tspan>
    <tspan x="580" dy="15">• Session management</tspan>
    <tspan x="580" dy="15">• Rate limiting protection</tspan>
    <tspan x="580" dy="25">📊 Security Testing:</tspan>
    <tspan x="580" dy="15">• SSL connection success: 100%</tspan>
    <tspan x="580" dy="15">• Authentication bypass: 0%</tspan>
    <tspan x="580" dy="15">• Data integrity verification: ✓</tspan>
  </text>
  
  <!-- Footer Section (10%) -->
  <rect x="20" y="520" width="801" height="60" fill="#ffffff" stroke="#2c3e50" stroke-width="2"/>
  
  <!-- Conclusion -->
  <text x="50" y="540" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2c3e50">
    Conclusion & Future Work
  </text>
  <text x="50" y="555" font-family="Arial, sans-serif" font-size="11" fill="#2c3e50">
    Successfully implemented a production-ready email system demonstrating network protocol mastery and security best practices.
  </text>
  <text x="50" y="570" font-family="Arial, sans-serif" font-size="11" fill="#2c3e50">
    Future enhancements: IMAP support, advanced spam filtering, mobile applications, and cloud deployment optimization.
  </text>
  
  <!-- Contact Information -->
  <text x="600" y="540" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2c3e50">
    Project Repository & Documentation
  </text>
  <text x="600" y="555" font-family="Arial, sans-serif" font-size="10" fill="#3498db">
    📁 GitHub: github.com/cs3611-email-system
  </text>
  <text x="600" y="570" font-family="Arial, sans-serif" font-size="10" fill="#3498db">
    📖 Documentation: Complete API docs and user manual included
  </text>
  
  <!-- QR Code Placeholder -->
  <rect x="750" y="535" width="40" height="40" fill="#2c3e50"/>
  <text x="770" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">QR</text>
  <text x="770" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">CODE</text>
  
  <!-- Decorative Elements -->
  <circle cx="80" cy="80" r="3" fill="#3498db"/>
  <circle cx="760" cy="80" r="3" fill="#3498db"/>
  <circle cx="80" cy="540" r="3" fill="#3498db"/>
  <circle cx="760" cy="540" r="3" fill="#3498db"/>
  
</svg>

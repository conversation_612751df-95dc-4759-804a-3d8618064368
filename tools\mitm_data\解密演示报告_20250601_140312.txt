中间人攻击解密演示报告
==================================================

1. 明文通信分析
------------------------------
总数据包: 28
明文数据包: 28
发现的敏感信息类型: 邮件主题, 认证信息
原始数据文件: D:\GitCode\cs3611_email\tools\mitm_data\拦截数据_20250601_140303.txt

明文数据包详情:
  数据包 1:
    时间: 2025-06-01T14:03:06.047188
    类型: SMTP明文
    大小: 43 字节
    内容: mail FROM:<<EMAIL>> size=667

...
    其他敏感信息: 发件人地址 -> <EMAIL>
    其他敏感信息: 发件人信息 -> <<EMAIL>> size=667

  数据包 2:
    时间: 2025-06-01T14:03:06.049778
    类型: 未知
    大小: 8 字节

  数据包 3:
    时间: 2025-06-01T14:03:06.050788
    类型: SMTP明文
    大小: 33 字节
    内容: rcpt TO:<<EMAIL>>

...
    其他敏感信息: 收件人地址 -> <EMAIL>
    其他敏感信息: 收件人信息 -> <<EMAIL>>

  数据包 4:
    时间: 2025-06-01T14:03:06.052785
    类型: 未知
    大小: 8 字节

  数据包 5:
    时间: 2025-06-01T14:03:06.053786
    类型: SMTP明文
    大小: 6 字节
    内容: data

...

  数据包 6:
    时间: 2025-06-01T14:03:06.054788
    类型: SMTP明文
    大小: 37 字节
    内容: 354 End data with <CR><LF>.<CR><LF>

...

  数据包 7:
    时间: 2025-06-01T14:03:06.055786
    类型: SMTP明文
    大小: 670 字节
    内容: Content-Type: multipart/mixed; boundary="===============6151803970626610409=="

MIME-Version: 1.0

M...
    邮件内容: 邮件头部编码 -> 5rWL6K+V5pS75Ye7IFsyMDI1LTA2LT...
    邮件内容: 邮件头部编码 -> 5rWL6K+V5Y+R6YCB6ICF...
    邮件内容: 邮件头部编码 -> 5rWL6K+V5o6l5pS26ICF...
    邮件内容: 邮件正文内容 -> 5pS75Ye75rWL6K+VCgrlj5HpgIHml7...
    邮件内容: 邮件正文内容 -> 5oSf5L+h5oGvOiDmmK8=...

  数据包 8:
    时间: 2025-06-01T14:03:06.067308
    类型: 未知
    大小: 35 字节

  数据包 9:
    时间: 2025-06-01T14:03:06.074826
    类型: 未知
    大小: 6 字节

  数据包 10:
    时间: 2025-06-01T14:03:06.075827
    类型: 未知
    大小: 9 字节


2. SSL加密通信分析
------------------------------
总数据包: 0
加密数据包: 0
明文数据包: 0
加密样本数量: 0
原始数据文件: D:\GitCode\cs3611_email\tools\mitm_data\拦截数据_20250601_140303.txt

加密数据包详情:

3. 安全结论
------------------------------
明文通信风险:
• 所有通信内容完全暴露
• 认证信息可被直接窃取
• 通信关系完全透明

SSL加密保护:
• 通信内容完全加密
• 认证过程受到保护
• 需要私钥才能解密

私钥泄露威胁:
• 历史通信可被解密
• 未来通信持续暴露
• 整个加密体系失效

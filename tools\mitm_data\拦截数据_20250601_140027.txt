
============================================================
时间戳: 2025-06-01T14:00:29.982515
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 39 字节
原始数据(HEX): 323230204c4150544f502d503231524534505520507974686f6e20534d545020312e342e360d0a
原始数据(UTF-8): 220 LAPTOP-P21RE4PU Python SMTP 1.4.6


============================================================

============================================================
时间戳: 2025-06-01T14:00:29.985522
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 22 字节
原始数据(HEX): 65686c6f205b3139322e3136382e3133372e315d0d0a
原始数据(UTF-8): ehlo [192.168.137.1]


============================================================

============================================================
时间戳: 2025-06-01T14:00:29.990525
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 21 字节
原始数据(HEX): 3235302d4c4150544f502d50323152453450550d0a
原始数据(UTF-8): 250-LAPTOP-P21RE4PU


============================================================

============================================================
时间戳: 2025-06-01T14:00:29.993546
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 65 字节
原始数据(HEX): 3235302d53495a452031303438353736300d0a3235302d534d5450555446380d0a3235302d41555448204c4f47494e20504c41494e0d0a3235302048454c500d0a
原始数据(UTF-8): 250-SIZE 10485760

250-SMTPUTF8

250-AUTH LOGIN PLAIN

250 HELP


============================================================

============================================================
时间戳: 2025-06-01T14:00:29.997539
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 4155544820504c41494e204148526c63335231633256794148526c6333527759584e7a0d0a
原始数据(UTF-8): AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz


🔓 认证信息已破解:
    🔓 SMTP PLAIN认证: 用户名='testuser', 密码='testpass'
============================================================

============================================================
时间戳: 2025-06-01T14:00:30.005555
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 41757468656e7469636174696f6e207375636365737366756c0d0a
原始数据(UTF-8): Authentication successful


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.007044
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 25 字节
原始数据(HEX): 41555448204c4f47494e206447567a6448567a5a58493d0d0a
原始数据(UTF-8): AUTH LOGIN dGVzdHVzZXI=


🔓 认证信息已破解:
    🔓 SMTP LOGIN用户名: testuser
============================================================

============================================================
时间戳: 2025-06-01T14:00:30.009052
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 35303320416c72656164792061757468656e746963617465640d0a
原始数据(UTF-8): 503 Already authenticated


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.011052
方向: 客户端->服务器
协议: 明文
类型: 未知
大小: 6 字节
原始数据(HEX): 717569740d0a
原始数据(UTF-8): quit


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.012051
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 9 字节
原始数据(HEX): 323231204279650d0a
原始数据(UTF-8): 221 Bye


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.030083
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 39 字节
原始数据(HEX): 323230204c4150544f502d503231524534505520507974686f6e20534d545020312e342e360d0a
原始数据(UTF-8): 220 LAPTOP-P21RE4PU Python SMTP 1.4.6


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.032091
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 22 字节
原始数据(HEX): 65686c6f205b3139322e3136382e3133372e315d0d0a
原始数据(UTF-8): ehlo [192.168.137.1]


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.034081
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 21 字节
原始数据(HEX): 3235302d4c4150544f502d50323152453450550d0a
原始数据(UTF-8): 250-LAPTOP-P21RE4PU


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.034081
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 65 字节
原始数据(HEX): 3235302d53495a452031303438353736300d0a3235302d534d5450555446380d0a3235302d41555448204c4f47494e20504c41494e0d0a3235302048454c500d0a
原始数据(UTF-8): 250-SIZE 10485760

250-SMTPUTF8

250-AUTH LOGIN PLAIN

250 HELP


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.035082
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 4155544820504c41494e204148526c63335231633256794148526c6333527759584e7a0d0a
原始数据(UTF-8): AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz


🔓 认证信息已破解:
    🔓 SMTP PLAIN认证: 用户名='testuser', 密码='testpass'
============================================================

============================================================
时间戳: 2025-06-01T14:00:30.041633
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 41757468656e7469636174696f6e207375636365737366756c0d0a
原始数据(UTF-8): Authentication successful


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.042634
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 25 字节
原始数据(HEX): 41555448204c4f47494e206447567a6448567a5a58493d0d0a
原始数据(UTF-8): AUTH LOGIN dGVzdHVzZXI=


🔓 认证信息已破解:
    🔓 SMTP LOGIN用户名: testuser
============================================================

============================================================
时间戳: 2025-06-01T14:00:30.044633
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 35303320416c72656164792061757468656e746963617465640d0a
原始数据(UTF-8): 503 Already authenticated


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.047639
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 43 字节
原始数据(HEX): 6d61696c2046524f4d3a3c7465737475736572406578616d706c652e636f6d3e2073697a653d3639310d0a
原始数据(UTF-8): mail FROM:<<EMAIL>> size=691


⚠️ 其他敏感信息:
    ⚠️ 发件人地址: <EMAIL>...
    ⚠️ 发件人信息: <<EMAIL>> size=691...
============================================================

============================================================
时间戳: 2025-06-01T14:00:30.048638
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 8 字节
原始数据(HEX): 323530204f4b0d0a
原始数据(UTF-8): 250 OK


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.049637
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 33 字节
原始数据(HEX): 7263707420544f3a3c726563697069656e74406578616d706c652e636f6d3e0d0a
原始数据(UTF-8): rcpt TO:<<EMAIL>>


⚠️ 其他敏感信息:
    ⚠️ 收件人地址: <EMAIL>...
    ⚠️ 收件人信息: <<EMAIL>>...
============================================================

============================================================
时间戳: 2025-06-01T14:00:30.050636
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 8 字节
原始数据(HEX): 323530204f4b0d0a
原始数据(UTF-8): 250 OK


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.051637
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 6 字节
原始数据(HEX): 646174610d0a
原始数据(UTF-8): data


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.052636
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 33353420456e6420646174612077697468203c43523e3c4c463e2e3c43523e3c4c463e0d0a
原始数据(UTF-8): 354 End data with <CR><LF>.<CR><LF>


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.053637
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 694 字节
原始数据(HEX): 436f6e74656e742d547970653a206d756c7469706172742f6d697865643b20626f756e646172793d223d3d3d3d3d3d3d3d3d3d3d3d3d3d3d353434343837323538393135373935323334353d3d220d0a4d494d452d56657273696f6e3a20312e300d0a4d6573736167652d49443a203c746573745f32303235303630315f313430303239406578616d706c652e636f6d3e0d0a5375626a6563743a203d3f7574662d383f423f3570793635612b47357061483571476a582b6131692b69766c513d3d3f3d0d0a46726f6d3a203d3f7574662d383f623f3572574c364b2b5635592b5236594342364943463f3d203c7465737475736572406578616d706c652e636f6d3e0d0a546f3a203d3f7574662d383f623f3572574c364b2b56356f366c35705332364943463f3d203c726563697069656e74406578616d706c652e636f6d3e0d0a446174653a2053756e2c203031204a756e20323032352031343a30303a3239202b303830300d0a0d0a2d2d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d353434343837323538393135373935323334353d3d0d0a436f6e74656e742d547970653a20746578742f706c61696e3b20636861727365743d227574662d38220d0a4d494d452d56657273696f6e3a20312e300d0a436f6e74656e742d5472616e736665722d456e636f64696e673a206261736536340d0a0d0a364c2b5a35706976354c69413562434235597946355a4372355a5747354c69613570793635612b47353571453659654e364b614236594b75354c753234344343437557476865577575652b380d0a6d756d55674f575572756156734f614e72754f41676557756f756149742b532f6f656142722b4f41676569306f75574b6f65614b7065575269754f4167673d3d0d0a0d0a2d2d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d353434343837323538393135373935323334353d3d2d2d0d0a2e0d0a
原始数据(UTF-8): Content-Type: multipart/mixed; boundary="===============5444872589157952345=="

MIME-Version: 1.0

Message-ID: <<EMAIL>>

Subject: =?utf-8?B?5py65a+G5paH5qGjX+a1i+ivlQ==?=

From: =?utf-8?b?5rWL6K+V5Y+R6YCB6ICF?= <<EMAIL>>

To: =?utf-8?b?5rWL6K+V5o6l5pS26ICF?= <<EMAIL>>

Date: Sun, 01 Jun 2025 14:00:29 +0800



--===============5444872589157952345==

Content-Type: text/plain; charset="utf-8"

MIME-Version: 1.0

Content-Transfer-Encoding: base64



6L+Z5piv5LiA5bCB5YyF5ZCr5ZWG5Lia5py65a+G55qE6YeN6KaB6YKu5Lu244CCCuWGheWuue+8

mumUgOWUruaVsOaNruOAgeWuouaIt+S/oeaBr+OAgei0ouWKoeaKpeWRiuOAgg==



--===============5444872589157952345==--

.


📧 邮件内容已解码:
    📧 邮件头部编码: 机密文档_测试...
    📧 邮件头部编码: 测试发送者...
    📧 邮件头部编码: 测试接收者...
    📧 邮件正文内容: 这是一封包含商业机密的重要邮件。
内容...
    📧 邮件正文内容: 销售数据、客户信息、财务报告。...
============================================================

============================================================
时间戳: 2025-06-01T14:00:30.066639
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 35 字节
原始数据(HEX): 323530204d65737361676520616363657074656420666f722064656c69766572790d0a
原始数据(UTF-8): 250 Message accepted for delivery


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.074634
方向: 客户端->服务器
协议: 明文
类型: 未知
大小: 6 字节
原始数据(HEX): 717569740d0a
原始数据(UTF-8): quit


============================================================

============================================================
时间戳: 2025-06-01T14:00:30.075635
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 9 字节
原始数据(HEX): 323231204279650d0a
原始数据(UTF-8): 221 Bye


============================================================

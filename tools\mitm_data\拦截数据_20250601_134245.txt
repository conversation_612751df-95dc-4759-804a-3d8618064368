
============================================================
时间戳: 2025-06-01T13:42:47.804684
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 39 字节
原始数据(HEX): 323230204c4150544f502d503231524534505520507974686f6e20534d545020312e342e360d0a
原始数据(UTF-8): 220 LAPTOP-P21RE4PU Python SMTP 1.4.6


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.808865
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 22 字节
原始数据(HEX): 65686c6f205b3139322e3136382e3133372e315d0d0a
原始数据(UTF-8): ehlo [192.168.137.1]


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.811834
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 21 字节
原始数据(HEX): 3235302d4c4150544f502d50323152453450550d0a
原始数据(UTF-8): 250-LAPTOP-P21RE4PU


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.812734
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 65 字节
原始数据(HEX): 3235302d53495a452031303438353736300d0a3235302d534d5450555446380d0a3235302d41555448204c4f47494e20504c41494e0d0a3235302048454c500d0a
原始数据(UTF-8): 250-SIZE 10485760

250-SMTPUTF8

250-AUTH LOGIN PLAIN

250 HELP


解码结果: {'解码成功': [{'类型': '可能的Base64数据', '原始': '10485760', '解码': 'N<羴'}, {'类型': '可能的Base64数据', '原始': 'SMTPUTF8', '解码': 'HQ1|'}], '解码失败': [{'类型': 'AUTH认证数据', '原始': 'PLAIN...', '错误': 'base64解码失败'}]}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.815740
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 4155544820504c41494e204148526c63335231633256794148526c6333527759584e7a0d0a
原始数据(UTF-8): AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz


解码结果: {'解码成功': [{'类型': 'AUTH认证数据', '原始': 'AHRlc3R1c2VyAHRlc3Rw...', '解码': '\x00testuser\x00testpass'}], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.824355
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 41757468656e7469636174696f6e207375636365737366756c0d0a
原始数据(UTF-8): Authentication successful


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.826880
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 25 字节
原始数据(HEX): 41555448204c4f47494e206447567a6448567a5a58493d0d0a
原始数据(UTF-8): AUTH LOGIN dGVzdHVzZXI=


解码结果: {'解码成功': [{'类型': 'AUTH认证数据', '原始': 'dGVzdHVzZXI=...', '解码': 'testuser'}], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.829876
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 35303320416c72656164792061757468656e746963617465640d0a
原始数据(UTF-8): 503 Already authenticated


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.832046
方向: 客户端->服务器
协议: 明文
类型: 未知
大小: 6 字节
原始数据(HEX): 717569740d0a
原始数据(UTF-8): quit


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.832950
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 9 字节
原始数据(HEX): 323231204279650d0a
原始数据(UTF-8): 221 Bye


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.839392
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 39 字节
原始数据(HEX): 323230204c4150544f502d503231524534505520507974686f6e20534d545020312e342e360d0a
原始数据(UTF-8): 220 LAPTOP-P21RE4PU Python SMTP 1.4.6


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.841797
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 22 字节
原始数据(HEX): 65686c6f205b3139322e3136382e3133372e315d0d0a
原始数据(UTF-8): ehlo [192.168.137.1]


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.842798
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 21 字节
原始数据(HEX): 3235302d4c4150544f502d50323152453450550d0a
原始数据(UTF-8): 250-LAPTOP-P21RE4PU


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.843797
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 65 字节
原始数据(HEX): 3235302d53495a452031303438353736300d0a3235302d534d5450555446380d0a3235302d41555448204c4f47494e20504c41494e0d0a3235302048454c500d0a
原始数据(UTF-8): 250-SIZE 10485760

250-SMTPUTF8

250-AUTH LOGIN PLAIN

250 HELP


解码结果: {'解码成功': [{'类型': '可能的Base64数据', '原始': '10485760', '解码': 'N<羴'}, {'类型': '可能的Base64数据', '原始': 'SMTPUTF8', '解码': 'HQ1|'}], '解码失败': [{'类型': 'AUTH认证数据', '原始': 'PLAIN...', '错误': 'base64解码失败'}]}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.847394
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 4155544820504c41494e204148526c63335231633256794148526c6333527759584e7a0d0a
原始数据(UTF-8): AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz


解码结果: {'解码成功': [{'类型': 'AUTH认证数据', '原始': 'AHRlc3R1c2VyAHRlc3Rw...', '解码': '\x00testuser\x00testpass'}], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.852535
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 41757468656e7469636174696f6e207375636365737366756c0d0a
原始数据(UTF-8): Authentication successful


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.853493
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 25 字节
原始数据(HEX): 41555448204c4f47494e206447567a6448567a5a58493d0d0a
原始数据(UTF-8): AUTH LOGIN dGVzdHVzZXI=


解码结果: {'解码成功': [{'类型': 'AUTH认证数据', '原始': 'dGVzdHVzZXI=...', '解码': 'testuser'}], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.854415
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 27 字节
原始数据(HEX): 35303320416c72656164792061757468656e746963617465640d0a
原始数据(UTF-8): 503 Already authenticated


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.858104
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 43 字节
原始数据(HEX): 6d61696c2046524f4d3a3c7465737475736572406578616d706c652e636f6d3e2073697a653d3636370d0a
原始数据(UTF-8): mail FROM:<<EMAIL>> size=667


解码结果: {'解码成功': [{'类型': '可能的Base64数据', '原始': 'testuser', '解码': '-ǫ'}], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.860043
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 8 字节
原始数据(HEX): 323530204f4b0d0a
原始数据(UTF-8): 250 OK


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.861021
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 33 字节
原始数据(HEX): 7263707420544f3a3c726563697069656e74406578616d706c652e636f6d3e0d0a
原始数据(UTF-8): rcpt TO:<<EMAIL>>


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.862022
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 8 字节
原始数据(HEX): 323530204f4b0d0a
原始数据(UTF-8): 250 OK


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.863015
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 6 字节
原始数据(HEX): 646174610d0a
原始数据(UTF-8): data


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.863997
方向: 服务器->客户端
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 33353420456e6420646174612077697468203c43523e3c4c463e2e3c43523e3c4c463e0d0a
原始数据(UTF-8): 354 End data with <CR><LF>.<CR><LF>


解码结果: {'解码成功': [], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.866626
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 670 字节
原始数据(HEX): 436f6e74656e742d547970653a206d756c7469706172742f6d697865643b20626f756e646172793d223d3d3d3d3d3d3d3d3d3d3d3d3d3d3d343735323030353331323130333837343436343d3d220d0a4d494d452d56657273696f6e3a20312e300d0a4d6573736167652d49443a203c746573745f32303235303630315f313334323437406578616d706c652e636f6d3e0d0a5375626a6563743a203d3f7574662d383f423f3572574c364b2b563570533735596537494673794d4449314c5441324c5441784944457a4f6a51794f6a517858513d3d3f3d0d0a46726f6d3a203d3f7574662d383f623f3572574c364b2b5635592b5236594342364943463f3d203c7465737475736572406578616d706c652e636f6d3e0d0a546f3a203d3f7574662d383f623f3572574c364b2b56356f366c35705332364943463f3d203c726563697069656e74406578616d706c652e636f6d3e0d0a446174653a2053756e2c203031204a756e20323032352031333a34323a3437202b303830300d0a0d0a2d2d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d343735323030353331323130333837343436343d3d0d0a436f6e74656e742d547970653a20746578742f706c61696e3b20636861727365743d227574662d38220d0a4d494d452d56657273696f6e3a20312e300d0a436f6e74656e742d5472616e736665722d456e636f64696e673a206261736536340d0a0d0a35705337355965373572574c364b2b564367726c6a3548706749486d6c3762706c375136494449774d6a55744d4459744d4445674d544d364e4449364e44454b35597946355a4372357057500d0a356f5366354c2b68356f47764f69446d6d4b383d0d0a0d0a2d2d3d3d3d3d3d3d3d3d3d3d3d3d3d3d3d343735323030353331323130333837343436343d3d2d2d0d0a2e0d0a
原始数据(UTF-8): Content-Type: multipart/mixed; boundary="===============4752005312103874464=="

MIME-Version: 1.0

Message-ID: <<EMAIL>>

Subject: =?utf-8?B?5rWL6K+V5pS75Ye7IFsyMDI1LTA2LTAxIDEzOjQyOjQxXQ==?=

From: =?utf-8?b?5rWL6K+V5Y+R6YCB6ICF?= <<EMAIL>>

To: =?utf-8?b?5rWL6K+V5o6l5pS26ICF?= <<EMAIL>>

Date: Sun, 01 Jun 2025 13:42:47 +0800



--===============4752005312103874464==

Content-Type: text/plain; charset="utf-8"

MIME-Version: 1.0

Content-Transfer-Encoding: base64



5pS75Ye75rWL6K+VCgrlj5HpgIHml7bpl7Q6IDIwMjUtMDYtMDEgMTM6NDI6NDEK5YyF5ZCr5pWP

5oSf5L+h5oGvOiDmmK8=



--===============4752005312103874464==--

.


解码结果: {'解码成功': [{'类型': '可能的Base64数据', '原始': '20250601', '解码': 'Mӭ5'}, {'类型': '可能的Base64数据', '原始': '5rWL6K+V5pS75Ye7IFsy...', '解码': '测试攻击 [2025-06-01 13:42:41]'}, {'类型': '可能的Base64数据', '原始': '5rWL6K+V5Y+R6YCB6ICF', '解码': '测试发送者'}, {'类型': '可能的Base64数据', '原始': 'testuser', '解码': '-ǫ'}, {'类型': '可能的Base64数据', '原始': '5rWL6K+V5o6l5pS26ICF', '解码': '测试接收者'}, {'类型': '可能的Base64数据', '原始': 'Transfer', '解码': 'N'}, {'类型': '可能的Base64数据', '原始': '5oSf5L+h5oGvOiDmmK8=', '解码': '感信息: 是'}], '解码失败': []}
============================================================

============================================================
时间戳: 2025-06-01T13:42:47.876221
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 35 字节
原始数据(HEX): 323530204d65737361676520616363657074656420666f722064656c69766572790d0a
原始数据(UTF-8): 250 Message accepted for delivery


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.884234
方向: 客户端->服务器
协议: 明文
类型: 未知
大小: 6 字节
原始数据(HEX): 717569740d0a
原始数据(UTF-8): quit


============================================================

============================================================
时间戳: 2025-06-01T13:42:47.885322
方向: 服务器->客户端
协议: 明文
类型: 未知
大小: 9 字节
原始数据(HEX): 323231204279650d0a
原始数据(UTF-8): 221 Bye


============================================================

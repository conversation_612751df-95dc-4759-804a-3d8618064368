# 真实中间人攻击演示工具 (Real MITM Attack Demonstration)

## 概述

这是一个基于项目现有邮件系统的真实中间人攻击演示工具，用于教学目的，展示SSL/TLS加密的重要性以及中间人攻击的真实威胁。

⚠️ **重要声明：本工具仅用于教学演示，请勿用于非法用途！**

## 🆕 最新改进 (2025-06-01)

### 1. 完全中文化界面
- **中文注释和输出**：所有代码注释、终端输出、类名和函数名均采用中文
- **中文日志信息**：便于中文用户理解和分析
- **中文报告生成**：生成详细的中文分析报告

### 2. 智能原始数据保存和解码功能 ⭐
- **智能解码系统**：避免盲目解码，只解码真正有意义的编码数据
- **分类解码分析**：
  - 🔓 **认证信息**：SMTP PLAIN/LOGIN认证的准确解码
  - 📧 **邮件内容**：RFC2047邮件头编码和Base64正文解码
  - ⚠️ **敏感信息**：发件人/收件人地址等明文信息提取
- **验证机制**：
  - 用户名格式验证（3-30字符，字母数字符号）
  - 文本意义验证（可打印字符比例>70%）
  - Base64完整性验证（长度、字符集、格式检查）
- **威胁级别标识**：极高/高/中级别的安全威胁分类

### 3. 演示说服力大幅提升
- **精确认证破解**：显示实际的用户名密码对（如：testuser/testpass）
- **邮件内容透明**：解码邮件主题、发送者、接收者信息
- **明文威胁展示**：清晰对比SSL vs 非SSL的安全差异
- **原始数据证据**：保存HEX和UTF-8格式的完整拦截证据

## 核心技术突破

### 智能解码算法

相比原始版本的盲目解码，新版本实现了以下技术突破：

#### 1. SMTP认证专项解码
```python
# AUTH PLAIN 格式解码：\x00username\x00password
if '\x00' in decoded:
    parts = decoded.split('\x00')
    用户名 = parts[1]  # testuser
    密码 = parts[2]    # testpass

# AUTH LOGIN Base64用户名验证
if self._是否为合理用户名(decoded):
    # 只有合理格式才记录
```

#### 2. RFC2047邮件头智能解码
```python
# 识别 =?charset?encoding?encoded-text?= 格式
rfc2047_pattern = r"=\?([^?]+)\?([BQ])\?([^?]+)\?="
# 验证解码结果的文本意义性
if self._是否为有意义文本(decoded):
    # 记录有效的邮件头信息
```

#### 3. Base64内容验证算法
```python
def _是否为有意义文本(self, text: str) -> bool:
    # 1. 长度检查
    if len(text.strip()) < 2: return False
    
    # 2. 可打印字符比例检查
    printable_ratio = sum(1 for c in text if c.isprintable()) / len(text)
    if printable_ratio < 0.7: return False
    
    # 3. 有意义字符检查（中英文）
    has_meaningful_chars = bool(re.search(r'[\u4e00-\u9fff\w\s]', text))
    return has_meaningful_chars
```

## 核心改进

相比原始版本，新版本具有以下关键改进：

### 1. 真实网络环境模拟
- 使用项目已有的 `StableSMTPServer` 和 `SMTPClient`
- 真实的网络通信和数据包拦截
- 独立进程间的真实通信

### 2. 严格的端口管理
- **SSL端口使用标准端口**：465 (SMTP SSL)
- **非SSL端口使用非标准端口**：8025 (SMTP Plain)
- **MITM代理端口**：8026 (Plain), 8466 (SSL)
- 避免端口冲突，符合网络安全标准

### 3. 真实的数据包分析
- 实时拦截和分析网络数据包
- 自动识别TLS握手、应用数据和SMTP命令
- 检测敏感信息（认证信息、邮件主题、机密内容）

### 4. 有说服力的演示效果
- 明文通信：完全暴露邮件内容和认证信息
- SSL通信：数据完全加密，无法直接读取
- 私钥泄露场景：模拟攻击者获得私钥后的解密能力

## 技术架构

### 核心组件

1. **网络流量分析器 (`网络流量分析器`)**
   - 实时分析网络数据包
   - 识别TLS/SSL协议特征
   - 检测SMTP命令和敏感信息
   - **新增**：智能Base64解码和数据保存

2. **真实MITM代理 (`真实MITM代理`)**
   - 真实的中间人代理
   - 双向数据转发和分析
   - 支持SSL和非SSL模式
   - **新增**：中文输出和详细分析报告

3. **真实邮件服务器管理器 (`真实邮件服务器管理器`)**
   - 管理SMTP服务器实例
   - 同时启动SSL和非SSL服务器
   - 端口状态验证

4. **真实邮件客户端模拟器 (`真实邮件客户端模拟器`)**
   - 模拟真实邮件客户端
   - 通过MITM代理发送邮件
   - 支持SSL和非SSL连接

### 端口配置

```python
PORTS = {
    "smtp_plain": 8025,      # 非SSL SMTP - 非标准端口
    "smtp_ssl": 465,         # SSL SMTP - 标准端口
    "mitm_smtp_plain": 8026, # MITM代理端口
    "mitm_smtp_ssl": 8466,   # MITM SSL代理端口
}
```

### 数据保存功能

```python
# 数据保存目录
DATA_DIR = Path(__file__).parent / "mitm_data"

# 自动生成的文件
- 拦截数据_{时间戳}.txt  # 原始数据包文件
- 解密演示报告_{时间戳}.txt  # 详细分析报告
```

## 使用方法

### 基本运行

```bash
python tools/realistic_mitm_demo.py
```

### 测试功能

```bash
python tools/test_mitm_demo.py
```

## 演示流程

### 阶段1：非SSL通信演示
1. 启动非SSL SMTP服务器 (端口8025)
2. 启动MITM代理 (端口8026)
3. 客户端通过代理发送邮件
4. **结果展示**：
   ```
   [明文_1] 数据包#2 (客户端->服务器): SMTP明文 - 37字节
     内容预览: AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz...
     发现敏感信息: 认证信息
     解码成功:
       AUTH认证数据: testuser\x00testpass
   ```

### 阶段2：SSL通信演示
1. 启动SSL SMTP服务器 (端口465)
2. 启动SSL MITM代理 (端口8466)
3. 客户端通过SSL代理发送邮件
4. **结果展示**：
   ```
   SSL通信分析结果:
     拦截数据包: 15
     加密数据包: 12
     内容保护: 完全加密
   ```

### 阶段3：私钥泄露场景
1. 模拟攻击者获得SSL私钥
2. 展示私钥泄露的严重后果
3. 说明历史通信记录的风险

## 演示输出示例

### 非SSL通信拦截（智能解码输出）
```
[明文_2] 数据包#2 (客户端->服务器): SMTP明文 - 37字节
  内容预览: AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz...
  发现敏感信息: 认证信息
🔓 认证信息已破解:
    🔓 SMTP PLAIN认证: 用户名='testuser', 密码='testpass'

[明文_2] 数据包#7 (客户端->服务器): SMTP明文 - 572字节
  内容预览: Content-Type: multipart/mixed; boundary="=========...
  发现敏感信息: 邮件主题
📧 邮件内容已解码:
    📧 邮件头部编码: 测试邮件
    📧 邮件头部编码: 测试发送者  
    📧 邮件头部编码: 测试接收者
    📧 邮件正文内容: 这是一封测试邮件
⚠️ 其他敏感信息:
    ⚠️ 发件人地址: <EMAIL>
    ⚠️ 收件人地址: <EMAIL>
```

### 原始数据保存格式（完整证据链）
```
============================================================
时间戳: 2025-06-01T13:58:01.014148
方向: 客户端->服务器
协议: 明文
类型: SMTP明文
大小: 37 字节
原始数据(HEX): 4155544820504c41494e204148526c63335231633256794148526c6333527759584e7a0d0a
原始数据(UTF-8): AUTH PLAIN AHRlc3R1c2VyAHRlc3RwYXNz

🔓 认证信息已破解:
    🔓 SMTP PLAIN认证: 用户名='testuser', 密码='testpass'
============================================================
```

### SSL通信拦截（加密保护）
```
SSL通信分析结果:
   拦截数据包: 15
   加密数据包: 12
   明文数据包: 3
   原始数据已保存到: D:\GitCode\cs3611_email\tools\mitm_data\拦截数据_*.txt

加密保护效果:
   • 邮件内容完全加密，无法直接读取
   • 认证过程受SSL保护
   • 通信内容对攻击者呈现为乱码
```

### 详细分析报告
```
中间人攻击解密演示报告
==================================================

1. 明文通信分析
------------------------------
总数据包: 27
明文数据包: 27
发现的敏感信息类型: 邮件主题, 认证信息

明文数据包详情:
  数据包 7:
    时间: 2025-06-01T13:38:59.947496
    类型: SMTP明文
    大小: 670 字节
    解码: 可能的Base64数据 -> 机密文档 [2025-06-01 13:38:53]
    解码: AUTH认证数据 -> testuser\x00testpass

3. 安全结论
------------------------------
明文通信风险:
• 所有通信内容完全暴露
• 认证信息可被直接窃取（用户名密码明文传输）
• 邮件主题和内容完全可读
• 通信关系完全透明
```

## 安全启示

### 1. 非SSL通信的极端危险性
通过智能解码系统，我们清晰展示了非SSL通信的严重威胁：

- **认证信息完全暴露**：
  - SMTP PLAIN认证：直接获得用户名密码对（`testuser/testpass`）
  - SMTP LOGIN认证：Base64编码的用户名轻易解码
  - 无任何加密保护，攻击者可直接使用窃取的凭据

- **邮件内容透明可读**：
  - RFC2047编码的中文邮件头：`测试邮件`、`测试发送者`
  - Base64编码的邮件正文：`这是一封测试邮件`
  - 发送方/接收方地址：完整的通信关系网

- **通信元数据暴露**：
  - 邮件大小、发送时间、路由信息
  - 通信频率和模式分析
  - 完整的数字足迹追踪

### 2. 智能解码 vs 盲目解码的价值
新的智能解码系统避免了误报和无意义输出：

- **精确性**：只解码真正有意义的认证和邮件数据
- **可信度**：通过多重验证确保解码结果的准确性
- **说服力**：提供具体的威胁证据而非模糊的可能性
- **教学价值**：清晰展示具体的安全风险点

### 3. SSL/TLS加密的关键保护作用
- SSL通信提供强有力的保护：
  - 认证过程完全加密，无法获取凭据
  - 邮件内容变为不可读的加密数据
  - 通信关系得到有效保护

### 4. 防护建议的重要性
- **技术层面**：
  - 强制使用SSL/TLS加密（端口465/993/995）
  - 实施证书固定和HSTS策略
  - 定期更新加密算法和证书

- **管理层面**：
  - 网络安全培训和意识教育
  - 定期安全审计和渗透测试
  - 制定应急响应预案

- **监控层面**：
  - 实时监控网络流量异常
  - 部署入侵检测和防护系统
  - 建立安全事件响应机制

## 技术细节

### 数据包分析算法

```python
def _是否为tls握手(self, data: bytes) -> bool:
    """检测TLS握手"""
    return len(data) >= 3 and data[0] == 0x16 and data[1:3] in [b'\x03\x01', b'\x03\x02', b'\x03\x03']

def _是否为tls应用数据(self, data: bytes) -> bool:
    """检测TLS应用数据"""
    return len(data) >= 3 and data[0] == 0x17

def _是否为smtp命令(self, data: bytes) -> bool:
    """检测SMTP命令"""
    try:
        text = data.decode('utf-8', errors='ignore').upper()
        smtp_commands = ['HELO', 'EHLO', 'AUTH', 'MAIL FROM', 'RCPT TO', 'DATA', 'SUBJECT:', 'FROM:', 'TO:']
        return any(cmd in text for cmd in smtp_commands)
    except:
        return False
```

### 智能解码功能

```python
def _尝试解码数据(self, 文本: str) -> dict:
    """尝试解码各种编码的数据"""
    解码结果 = {"解码成功": [], "解码失败": []}
    
    # 查找AUTH命令中的base64数据
    auth_pattern = r'AUTH\s+\w+\s+([A-Za-z0-9+/=]+)'
    auth_matches = re.findall(auth_pattern, 文本, re.IGNORECASE)
    
    for match in auth_matches:
        try:
            decoded = base64.b64decode(match).decode('utf-8')
            解码结果["解码成功"].append({
                "类型": "AUTH认证数据",
                "原始": match[:20] + "...",
                "解码": decoded  # 实际解码如：\x00testuser\x00testpass
            })
        except:
            解码结果["解码失败"].append({...})
    
    return 解码结果
```

### 敏感信息检测

```python
def _检测敏感信息(self, text: str) -> list:
    """检测敏感信息"""
    敏感信息 = []
    text_lower = text.lower()
    
    if 'auth' in text_lower:
        敏感信息.append("认证信息")
    if 'password' in text_lower:
        敏感信息.append("密码")
    if 'subject:' in text_lower:
        敏感信息.append("邮件主题")
    if any(keyword in text_lower for keyword in ['confidential', 'secret', '机密', '秘密']):
        敏感信息.append("机密内容")
        
    return 敏感信息
```

## 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口状态
   netstat -an | findstr "8025\|465\|8026\|8466"
   ```

2. **SSL证书问题**
   - 确保 `certs/server.crt` 和 `certs/server.key` 存在
   - 检查证书有效性

3. **邮件发送失败**
   - 检查邮箱格式是否正确
   - 确认服务器正常启动

4. **数据保存问题**
   - 确保 `tools/mitm_data` 目录存在
   - 检查文件写入权限

### 调试模式

启用详细日志：
```python
logger = setup_logging("realistic_mitm", verbose=True)
```

查看保存的数据：
```bash
# 查看拦截的原始数据
cat tools/mitm_data/拦截数据_*.txt

# 查看分析报告
cat tools/mitm_data/解密演示报告_*.txt
```

## 教学应用

### 适用场景
- 网络安全课程演示
- SSL/TLS原理教学
- 中间人攻击防护培训
- 邮件安全意识教育
- 密码学实践教学

### 学习目标
- 理解中间人攻击原理和实际威胁
- 认识SSL/TLS加密的重要性
- 掌握网络安全防护措施
- 培养安全意识和最佳实践
- 了解数据包分析和解密技术

### 教学价值
1. **直观演示**：通过实际拦截的数据包展示安全威胁
2. **对比分析**：SSL vs 非SSL的安全差异清晰可见
3. **实战模拟**：真实的网络环境和攻击场景
4. **深入分析**：详细的数据解码和安全分析报告

## 文件结构

```
tools/
├── realistic_mitm_demo.py      # 主演示脚本
├── test_mitm_demo.py          # 测试脚本
├── MITM_DEMO_README.md        # 说明文档
└── mitm_data/                 # 数据保存目录
    ├── 拦截数据_时间戳.txt     # 原始数据包
    └── 解密演示报告_时间戳.txt  # 分析报告
```

## 法律声明

本工具仅用于教学和研究目的。使用者必须：
- 仅在授权环境中使用
- 不得用于非法攻击
- 遵守当地法律法规
- 承担使用责任

## 技术支持

如有问题或建议，请联系项目维护团队。

---

**版本**: 3.0  
**更新日期**: 2025-06-01  
**维护者**: CS3611 Project Team  
**新增功能**: 中文界面、数据保存、智能解码、详细分析报告